# iframe预加载Hook - 简化版

## 概述

重写了 `useIframePreloader` Hook，采用更简单的实现方式，通过 `isLoaded` 状态来控制iframe的显示。

## 核心特性

### 🎯 **简化的状态管理**
- `isLoading`: 是否正在加载
- `isLoaded`: 是否加载完成 
- `shouldShow`: 是否应该显示（基于路由匹配）
- `error`: 错误信息

### 🚀 **核心逻辑**
只有当 `shouldShow && isLoaded` 都为 `true` 时，iframe才会显示。

## 使用方式

### 1. Hook调用
```tsx
const {isLoading, isLoaded, shouldShow, error} = useIframePreloader({
  iframeId: 'edap-iframe',           // iframe的ID
  targetPath: '/edap-workspace',     // 目标路由路径
  currentPath: location.pathname,    // 当前路由路径
  containerId: 'iframe-div-box',     // iframe容器的ID
  timeout: 30000                     // 超时时间（可选）
});
```

### 2. 组件渲染
```tsx
return (
  <div className="iframe-page-box">
    {isLoading && (
      <div className="iframe-loading-overlay">
        <Loading loading />
      </div>
    )}
    
    <div
      id="iframe-div-box"  // 重要：ID必须与Hook中的containerId匹配
      style={{
        width: '100%',
        height: '100%',
        opacity: isLoaded ? 1 : 0,
        transition: 'opacity 0.3s ease-in-out'
      }}
    />
  </div>
);
```

## 工作原理

### 1. **预加载阶段**
- iframe在应用启动时由 `IframePreloader` 组件创建
- iframe隐藏在 `document.body` 中，开始加载资源
- Hook监听iframe的 `load` 事件

### 2. **路由匹配检查**
- `shouldShow = currentPath === targetPath`
- 只有路由匹配时才考虑显示iframe

### 3. **显示控制**
- 当 `shouldShow && isLoaded` 时：
  - 将iframe从body移动到指定容器
  - 设置iframe为可见状态
- 否则：
  - 隐藏iframe并移回body

### 4. **状态更新**
- `isLoading`: 初始为true，加载完成后为false
- `isLoaded`: 加载成功后为true
- `error`: 加载失败或超时时设置错误信息

## 关键改进

### ✅ **简化的API**
- 移除了复杂的回调函数
- 直接返回状态值，使用更简单

### ✅ **自动控制**
- Hook内部自动处理iframe的显示/隐藏
- 无需手动调用显示/隐藏方法

### ✅ **基于isLoaded的显示**
- 只有当iframe真正加载完成时才显示
- 避免显示空白或加载中的iframe

### ✅ **更好的性能**
- 减少了不必要的DOM操作
- 更清晰的状态管理

## 注意事项

### 1. **容器ID匹配**
确保容器元素的ID与Hook中的 `containerId` 参数匹配：
```tsx
// Hook中
containerId: 'iframe-div-box'

// JSX中
<div id="iframe-div-box" />
```

### 2. **路由路径匹配**
确保 `targetPath` 与实际路由路径完全匹配：
```tsx
targetPath: '/edap-workspace'  // 必须与路由配置一致
```

### 3. **iframe预创建**
确保 `IframePreloader` 组件已经在应用中正确集成，用于预创建iframe。

## 完整示例

```tsx
import React, {FC} from 'react';
import {useLocation} from 'react-router-dom';
import {Loading} from 'acud';
import {useIframePreloader} from '@hooks/useIframePreloader';

const IframePageView: FC = () => {
  const location = useLocation();
  
  const {isLoading, isLoaded, shouldShow, error} = useIframePreloader({
    iframeId: 'edap-iframe',
    targetPath: '/edap-workspace',
    currentPath: location.pathname,
    containerId: 'iframe-div-box',
    timeout: 30000
  });

  if (!shouldShow) {
    return null;
  }

  if (error) {
    return (
      <div className="iframe-page-box">
        <div className="iframe-error-overlay">
          <div className="error-content">
            <h3>加载失败</h3>
            <p>{error}</p>
            <button onClick={() => window.location.reload()}>
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="iframe-page-box">
      {isLoading && (
        <div className="iframe-loading-overlay">
          <Loading loading />
        </div>
      )}
      
      <div
        id="iframe-div-box"
        style={{
          width: '100%',
          height: '100%',
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out'
        }}
      />
    </div>
  );
};

export default React.memo(IframePageView);
```

## 总结

简化版的 `useIframePreloader` Hook 提供了更直观的API和更简单的使用方式。通过 `isLoaded` 状态来控制iframe显示，确保只有在iframe完全加载后才展示给用户，提供了更好的用户体验。
