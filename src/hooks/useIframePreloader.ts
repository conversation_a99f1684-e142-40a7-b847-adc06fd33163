/**
 * iframe预加载管理Hook - 简化版
 */

import {useEffect, useState} from 'react';

interface IframePreloaderOptions {
  /** iframe的ID */
  iframeId: string;
  /** 目标路由路径 */
  targetPath: string;
  /** 当前路由路径 */
  currentPath: string;
  /** iframe 父标签容器的ID */
  containerId: string;
  /** 预加载超时时间（毫秒） */
  timeout?: number;
}

export const useIframePreloader = ({
  iframeId,
  targetPath,
  currentPath,
  containerId,
  timeout = 30000
}: IframePreloaderOptions) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 检查当前路由是否匹配目标路径
  const shouldShow = currentPath === targetPath;

  // 监听iframe加载状态
  useEffect(() => {
    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;

    if (!iframe) {
      setError(`iframe with id "${iframeId}" not found`);
      setIsLoading(false);
      return;
    }

    let timeoutId: number;

    const handleLoad = () => {
      clearTimeout(timeoutId);
      setIsLoading(false);
      setIsLoaded(true);
      setError(null);
    };
    iframe && setTimeout(() => handleLoad(), 1000);

    const handleError = () => {
      clearTimeout(timeoutId);
      setIsLoading(false);
      setIsLoaded(false);
      setError('iframe failed to load');
    };

    // 检查iframe是否已经加载完成
    // if (iframe.contentDocument?.readyState === 'complete') {

    // } else {
    //   设置超时
    //   timeoutId = setTimeout(() => {
    //     setIsLoading(false);
    //     setIsLoaded(false);
    //     setError('iframe load timeout');
    //   }, timeout);

    //   iframe.addEventListener('load', handleLoad);
    //   iframe.addEventListener('error', handleError);
    // }

    // return () => {
    //   clearTimeout(timeoutId);
    //   iframe.removeEventListener('load', handleLoad);
    //   iframe.removeEventListener('error', handleError);
    // };
  }, [iframeId, timeout]);

  // 根据路由状态和加载状态控制iframe显示
  useEffect(() => {
    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
    if (!iframe) return;

    if (shouldShow && isLoaded) {
      // 显示iframe时，将其移动到当前容器
      const container = document.getElementById(containerId);
      if (container && iframe.parentNode !== container) {
        container.appendChild(iframe);
      }
      iframe.style.display = 'block';
      iframe.style.width = '100%';
      iframe.style.height = '100%';
    }
    return () => {
      // 隐藏iframe时，将其移回body但保持隐藏状态
      iframe.style.display = 'none';
      if (iframe.parentNode !== document.body) {
        document.body.appendChild(iframe);
      }
    };
  }, [shouldShow, isLoaded, iframeId, containerId]);

  return {
    isLoading,
    isLoaded,
    shouldShow,
    error
  };
};
